import type { VendorType } from '@/types';
import { Divider, Image, Menu, Text, UnstyledButton } from '@mantine/core';
import { MODAL_NAME } from '@/constants';
import { Button, type ButtonProps } from '@/libs/ui/Button/Button';
import { Alert } from '@/libs/ui/Alert/Alert';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { CatalogSyncStatus } from '../CatalogSyncStatus/CatalogSyncStatus';
import { VendorConnectionStatus } from '../VendorConnectionStatus/VendorConnectionStatus';
import { BetaBadge } from '../BetaBadge/BetaBadge';
import styles from './VendorItem.module.css';
import dayjs from 'dayjs';
import { RecommendedTag } from '@/libs/ui/RecommendedTag/RecommendedTag';
import { Icon } from '@/libs/icons/Icon';
import { CatalogSyncStatusText } from '../CatalogSyncStatusText/CatalogSyncStatusText';

interface VendorItemProps {
  vendor: VendorType;
  isPreferred: boolean;
}
export const VendorItem = ({ vendor, isPreferred }: VendorItemProps) => {
  const { openModal } = useModalStore();

  const handleOpenModal = (vendor: VendorType) => {
    openModal({ name: MODAL_NAME.VENDOR_CONNECT, vendor });
  };

  const {
    id,
    alert,
    imageUrl,
    name,
    lastProductCatalogSync,
    status,
    type,
    integrationPoints,
  } = vendor;

  // Check if vendor is beta: doesn't have both sync_product_catalog AND place_orders
  const isBeta =
    !integrationPoints.includes('sync_product_catalog') ||
    !integrationPoints.includes('place_orders');

  const connectCTA: {
    show: boolean;
    label: string;
    variant: ButtonProps['variant'];
  } = {
    show:
      lastProductCatalogSync?.status === 'failed' || status === 'disconnected',
    label: lastProductCatalogSync ? 'Update Credentials' : 'Connect',
    variant: lastProductCatalogSync ? 'secondary' : 'default',
  };

  const isMoreOptionsDisabled =
    status === 'connecting' ||
    (status === 'disconnected' && !lastProductCatalogSync);

  const hasNoProductCatalogSync =
    status === 'connected' &&
    !integrationPoints.includes('sync_product_catalog');

  return (
    <div className="mb-10 w-80 rounded-sm border border-[#0072C6] px-4 pt-6 pb-4">
      <div className="flex items-start">
        <img src={imageUrl} alt="Vendor Logo" className="w-16 object-contain" />
        <div className="ml-4 flex flex-col">
          <p className="text-sxs font-semibold text-[#3646AC]">
            Primary Distributor
          </p>
          <p className="text-[16px] font-medium">
            {name} {isBeta && <BetaBadge />}
          </p>
          <div className="flex">
            <span className="text-sxs mr-1 text-black/70">Category:</span>
            <span className="text-sxs font-medium text-black capitalize">
              {type}
            </span>
          </div>
        </div>
        <Menu>
          <Menu.Target>
            <Button
              variant="unstyled"
              disabled={isMoreOptionsDisabled}
              aria-label="Vendor menu options"
              className="top-4 ml-auto"
            >
              <Icon name="moreOptions" color="#333" aria-hidden={true} />
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Label>
              <UnstyledButton onClick={() => handleOpenModal(vendor)}>
                <p className="text-sm font-medium text-black">
                  Edit Credentials
                </p>
              </UnstyledButton>
            </Menu.Label>
          </Menu.Dropdown>
        </Menu>
      </div>
      <div className="divider-h my-2"></div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-black/70">Catalog Sync</p>
        <CatalogSyncStatus
          lastProductCatalogSync={lastProductCatalogSync}
          hasNoProductCatalogSync={hasNoProductCatalogSync}
        />
      </div>
      <CatalogSyncStatusText lastProductCatalogSync={lastProductCatalogSync} />
      <div className="divider-h my-2"></div>
      <div className="my-4">
        {connectCTA.show ? (
          <Button
            onClick={() => handleOpenModal(vendor)}
            variant={connectCTA.variant}
          >
            {connectCTA.label}
          </Button>
        ) : (
          <VendorConnectionStatus status={status} />
        )}
      </div>
    </div>
  );
};
