import { CatalogSyncStatusType, VendorType } from '@/types';

const syncStatusConfigs: Record<
  CatalogSyncStatusType,
  {
    backgroundColor: string;
    borderColor: string;
  }
> = {
  pending: {
    backgroundColor: 'bg-[#B6F5F940]',
    borderColor: 'border-[#A1DBDE80]',
    Text: (
      <p className="text-xs">
        <span className="font-medium">
          Great news! We’re connecting you with your vendor
        </span>{' '}
        — this may take a few minutes. Feel free to check back in a bit if you’d
        like.
      </p>
    ),
  },
  succeeded: {
    backgroundColor: 'bg-[#FAD6CF40]',
    borderColor: 'border-[#FAD6CF40]',
    Text: (
      <p className="text-xs">
        <span className="font-medium">
          Oops, something went wrong when we tried to connect.
        </span>{' '}
        Please check your credentials and try again, and if the problem
        persists, feel free to ask for help here.
      </p>
    ),
  },
};

export const CatalogSyncStatusText = ({
  lastProductCatalogSync,
}: {
  lastProductCatalogSync: VendorType['lastProductCatalogSync'];
}) => {
  if (
    !lastProductCatalogSync ||
    (lastProductCatalogSync.status !== 'succeeded' &&
      lastProductCatalogSync.status !== 'failed')
  ) {
    return null;
  }

  return (
    <div
      className={`my-4 rounded-sm border p-4 ${syncStatusConfigs[lastProductCatalogSync.status].backgroundColor} ${syncStatusConfigs[lastProductCatalogSync.status].borderColor}`}
    >
      {syncStatusConfigs[lastProductCatalogSync.status].Text}
    </div>
  );
};
