import dayjs from 'dayjs';
import React from 'react';

export const LastUpdated = ({ updatedAt }: { updatedAt: string }) => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="4"
        height="4"
        viewBox="0 0 4 4"
        fill="none"
      >
        <circle cx="2" cy="2" r="2" fill="#D9D9D9" />
      </svg>
      <p className="text-sxs text-black/70">
        <span className="font-medium">Last update:</span>
        {' ' + dayjs(updatedAt).format('MMMM D, YYYY')}
      </p>
    </>
  );
};
