import type { VendorType } from '@/types';
import { Badge } from '@/libs/ui/Badge/Badge';

interface VendorConnectionStatusProps {
  status: VendorType['status'];
}
export const VendorConnectionStatus = ({
  status,
}: VendorConnectionStatusProps) => {
  const statusConfigs: Record<VendorType['status'], string> = {
    connected: 'text-[#4A8B34] border border-[#4A8B34] capitalize',
    connecting: 'bg-[#B6F5F9] text-[#344054]',
    disconnected: '',
  };

  const style = statusConfigs[status];

  if (style) {
    return <Badge className={style}>{status}</Badge>;
  }

  return null;
};
