import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { fetchApi } from '@/libs/utils/api';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { createStore } from '@/utils';

import {
  Actions,
  ConnectVendorParams,
  ConnectAmazonVendorParams,
  State,
} from './types';
import { VendorType } from '@/types';

export const INITIAL_STATE: State = {
  vendors: [],
  isLoading: false,
};

export const useVendorsStore = createStore<State & Actions>()(
  immer(
    devtools((set) => ({
      ...INITIAL_STATE,
      getVendors: async () => {
        const activeClinic = useAccountStore.getState().activeClinic;

        const clinicId = activeClinic?.id;

        if (!clinicId) {
          return;
        }

        set({ isLoading: true });

        const response = [
          {
            id: '9d7559b3-1c5a-487b-982e-e57510258c5e',
            name: 'Amatheon',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/amatheon.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-4a1d-4b91-8e02-e5834c02b2f5',
            name: 'Amazon',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/amazon.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'OAUTH2',
            authenticationConfiguration: {
              authorizationUri:
                'https://www.amazon.com/b2b/abws/oauth?state=9d7559be-6985-4478-83b2-f6f303c98f58&redirect_uri=https://services.highfive.vet/api/oauth2/amazon-business&applicationId=amzn1.sp.solution.b83d6cb9-da87-4673-ab22-f4854a79d9a4',
            },
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9f667890-30a8-42e1-b3d9-6e2c897d112a',
            name: 'CEVA',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/uDdmvNEsUlmaCvsgppQTPYc98EejBbRgrrZWpncX.jpg',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d7559b3-0b71-4606-88b0-e903fc518846',
            name: 'Covetrus',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
            status: 'connected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: {
              id: '9ec38365-45e3-4b2b-af22-c6622716533a',
              status: 'succeeded',
              createdAt: '2025-04-26T00:00:02.000000Z',
              updatedAt: '2025-04-26T00:00:02.000000Z',
            },
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d7559b3-5592-4c58-a47c-0bfbc7631283',
            name: 'Dr. Xie Jing Tang Herbal Store',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/dr-xie-jing-tang.png',
            status: 'connected',
            alert: {
              id: '01990fa2-80f1-7041-b90d-c562cfda72c4',
              integrationConnectionId: '01990fa2-80eb-703f-bc8f-692a62ff1466',
              type: 'info',
              message:
                '**Great news! Weu2019re connecting you with your vendor** u2014 this may take a few minutes. Feel free to check back in a bit if youu2019d like.',
              createdAt: '2025-09-03T12:52:12.000000Z',
              updatedAt: '2025-09-03T12:52:12.000000Z',
            },
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-2ab4-4966-8b8c-46e58c977923',
            name: 'Elanco',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/elanco-animal-health.png',
            status: 'connected',
            alert: {
              id: '01990fa2-d2fb-734e-9ab6-41f5f6738758',
              integrationConnectionId: '01990fa2-d2f6-73c3-9ab9-9e065f8a8c1c',
              type: 'info',
              message:
                '**Great news! Weu2019re connecting you with your vendor** u2014 this may take a few minutes. Feel free to check back in a bit if youu2019d like.',
              createdAt: '2025-09-03T12:52:33.000000Z',
              updatedAt: '2025-09-03T12:52:33.000000Z',
            },
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9dfcbf16-e437-411a-abf1-f159f5bac857',
            name: 'Equine Light Therapy',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/7adRK1DuUnoArXgJ14x7lYTMYBAvlJSyQN5o1Q4m.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-147f-4375-adbf-b4daf0736030',
            name: 'First Vet',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/firstvet.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-2c42-4ba6-941f-60d0425ab632',
            name: "Hill's Pet Nutrition",
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/hills-pet-nutrition.png',
            status: 'connecting',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: {
              id: '9fca6e8e-d053-481e-be6e-773fc6758e76',
              status: 'succeeded',
              createdAt: '2025-09-03T16:44:30.000000Z',
              updatedAt: '2025-09-03T17:04:31.000000Z',
            },
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d7559b3-3e3a-4e80-ae08-7118a8a8430b',
            name: 'Idexx',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/idexx.png',
            status: 'connected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: {
              id: '9ecf954d-0fb7-4a12-b3ed-3c4cd08692f3',
              status: 'succeeded',
              createdAt: '2025-05-02T00:00:02.000000Z',
              updatedAt: '2025-05-02T10:01:47.000000Z',
            },
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d83a84e-3ff7-4eb0-80e9-a81ab6e01dd1',
            name: "Kona's Chips",
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/8By6iXzxeTJT5f1OoORZj9K8P4RNDUvjQ2xoi6pY.webp',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-5410-45ae-8ace-2e8e2e5aeaa2',
            name: 'Lhasa Oms',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/lhasa-oms.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-0737-464e-91ba-739b63ec41cd',
            name: 'MWI',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/mwi.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d7559b3-0dd8-439f-993d-a08ce4bdd788',
            name: 'Midwest',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/midwest.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d9410af-025d-4a32-8648-51e049e9c332',
            name: 'Myos Pet',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/v6DGY4lSzGfVKchihniUJd0oHPNkoH9VErjPth2w.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-2368-442a-b6d3-4e137631d684',
            name: 'New England Animal Health',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/new-england-animal-health.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9de8a8aa-3c08-4778-9a05-fcb87e8eb451',
            name: 'Ortho Pets by Dassiet',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/KiBm5h9UuUIFkRBoXGnOuoLPdXuKA6E7UWkcKgLv.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
            name: 'Patterson',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
            status: 'connected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: {
              id: '9ecf954d-1b5e-471f-8362-1febfb9c5ea9',
              status: 'succeeded',
              createdAt: '2025-05-02T00:00:02.000000Z',
              updatedAt: '2025-05-02T10:01:54.000000Z',
            },
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9dfa1e3c-b309-4f53-869f-16d72d78e24a',
            name: 'Paw Prosper',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/KeULmAFtddF3ZBy3Gwqs3Qky9GrPFFrD3mrIa9pk.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-1edb-4a80-855f-83ee90d38cdf',
            name: 'Pharmsource AH',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/pharmasource.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-5297-4046-b1da-505a0132423a',
            name: 'Right Ratio',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/ZoZwwTDzzGdRpl4NemlEvxRIQ306i1PtWNcU0EzQ.webp',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-513a-4c79-90fd-74fdec01d588',
            name: 'Standard Process',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/standard-process.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9de03d5f-7f60-4b8e-aaf5-1fdbc3cdeb0d',
            name: 'TENS pros',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/AzixOC4EJpqihkgZ603HBW6btIaV4fGJGP6R73kB.webp',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-4fdb-4328-99c4-c714fa593bf3',
            name: 'VRS',
            type: 'distributor',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/vrs.png',
            status: 'disconnected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
          {
            id: '9d7559b3-3486-490b-bf63-891ff4f5082f',
            name: 'Wedgewood Pharmacy',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/wedgewood-pharmacy.png',
            status: 'connected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: {
              id: '9ed59e40-f42c-431a-be3a-b26a8525566b',
              status: 'succeeded',
              createdAt: '2025-05-05T00:00:02.000000Z',
              updatedAt: '2025-05-05T13:33:36.000000Z',
            },
            integrationPoints: ['sync_product_catalog'],
          },
          {
            id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
            name: 'Zoetis',
            type: 'manufacturer',
            imageUrl:
              'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
            status: 'connected',
            alert: null,
            authenticationKind: 'BASIC',
            authenticationConfiguration: null,
            lastProductCatalogSync: null,
            integrationPoints: [],
          },
        ];

        set({ isLoading: false });
        set({ vendors: response });
      },
      connectVendor: async (params: ConnectVendorParams) => {
        let clinicId: string | undefined = '';

        const activeClinic = useAccountStore.getState().activeClinic;

        clinicId = activeClinic?.id;

        const updatedVendors = await fetchApi<VendorType[]>(
          `/clinics/${clinicId}/vendors`,
          {
            method: 'POST',
            body: {
              vendorId: params.vendorId,
              credentials: {
                username: params.username,
                password: params.password,
              },
            },
          },
        );

        await useClinicStore.getState().getClinic(clinicId);

        set({ vendors: updatedVendors });
      },
      connectAmazonVendor: async (params: ConnectAmazonVendorParams) => {
        const { redirectUri } = await fetchApi<{
          redirectUri: string;
          status: string;
        }>(`/integrations/${params.vendorId}/connect`, {
          method: 'POST',
          body: {
            buyingGroupId: params.buyingGroupId,
          },
        });

        return { redirectUri };
      },
    })),
  ),
);
